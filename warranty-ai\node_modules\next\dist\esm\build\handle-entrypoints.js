import { getEntryKey } from '../shared/lib/turbopack/entry-key';
import * as Log from './output/log';
export async function rawEntrypointsToEntrypoints(entrypointsOp) {
    const page = new Map();
    const app = new Map();
    for (const [pathname, route] of entrypointsOp.routes){
        switch(route.type){
            case 'page':
            case 'page-api':
                page.set(pathname, route);
                break;
            case 'app-page':
                {
                    for (const p of route.pages){
                        app.set(p.originalName, {
                            type: 'app-page',
                            ...p
                        });
                    }
                    break;
                }
            case 'app-route':
                {
                    app.set(route.originalName, route);
                    break;
                }
            default:
                Log.info(`skipping ${pathname} (${route.type})`);
                break;
        }
    }
    return {
        global: {
            app: entrypointsOp.pagesAppEndpoint,
            document: entrypointsOp.pagesDocumentEndpoint,
            error: entrypointsOp.pagesErrorEndpoint,
            instrumentation: entrypointsOp.instrumentation,
            middleware: entrypointsOp.middleware
        },
        page,
        app
    };
}
export async function handleRouteType({ page, route, manifestLoader }) {
    const shouldCreateWebpackStats = process.env.TURBOPACK_STATS != null;
    switch(route.type){
        case 'page':
            {
                const serverKey = getEntryKey('pages', 'server', page);
                await manifestLoader.loadBuildManifest(page);
                await manifestLoader.loadPagesManifest(page);
                const middlewareManifestWritten = await manifestLoader.loadMiddlewareManifest(page, 'pages');
                if (!middlewareManifestWritten) {
                    manifestLoader.deleteMiddlewareManifest(serverKey);
                }
                await manifestLoader.loadFontManifest('/_app', 'pages');
                await manifestLoader.loadFontManifest(page, 'pages');
                if (shouldCreateWebpackStats) {
                    await manifestLoader.loadWebpackStats(page, 'pages');
                }
                break;
            }
        case 'page-api':
            {
                const key = getEntryKey('pages', 'server', page);
                await manifestLoader.loadPagesManifest(page);
                const middlewareManifestWritten = await manifestLoader.loadMiddlewareManifest(page, 'pages');
                if (!middlewareManifestWritten) {
                    manifestLoader.deleteMiddlewareManifest(key);
                }
                break;
            }
        case 'app-page':
            {
                const key = getEntryKey('app', 'server', page);
                const middlewareManifestWritten = await manifestLoader.loadMiddlewareManifest(page, 'app');
                if (!middlewareManifestWritten) {
                    manifestLoader.deleteMiddlewareManifest(key);
                }
                await manifestLoader.loadAppBuildManifest(page);
                await manifestLoader.loadBuildManifest(page, 'app');
                await manifestLoader.loadAppPathsManifest(page);
                await manifestLoader.loadActionManifest(page);
                await manifestLoader.loadFontManifest(page, 'app');
                if (shouldCreateWebpackStats) {
                    await manifestLoader.loadWebpackStats(page, 'app');
                }
                break;
            }
        case 'app-route':
            {
                const key = getEntryKey('app', 'server', page);
                await manifestLoader.loadAppPathsManifest(page);
                const middlewareManifestWritten = await manifestLoader.loadMiddlewareManifest(page, 'app');
                if (!middlewareManifestWritten) {
                    manifestLoader.deleteMiddlewareManifest(key);
                }
                break;
            }
        default:
            {
                throw Object.defineProperty(new Error(`unknown route type ${route.type} for ${page}`), "__NEXT_ERROR_CODE", {
                    value: "E316",
                    enumerable: false,
                    configurable: true
                });
            }
    }
}

//# sourceMappingURL=handle-entrypoints.js.map