# WarrantyAI MVP Build Specification
## 🎯 Objective
Build a **production-ready, futuristic MVP** for WarrantyAI using Next.js + Tailwind CSS. Zero templates, unlimited effects, working demos.

## 💡 STARTUP: WarrantyAI
**Smart AI assistant** to track warranties, services, and coverage across electronics, home, vehicles, appliances, and food by scanning receipts/photos. Future AR/3D inventory.

**Core Problem**: People lose receipts, forget warranties, miss service windows  
**Solution**: AI-powered upload → extract product/warranty info → smart reminders → 3D/AR inventory  
**Target**: Consumers, families, homeowners managing 25+ items with warranties  
**MVP Features**: Manual upload, AI extraction, dashboard reminders, claim assistant  

**HERO DEMO REQUIREMENT**: Working simulation showing receipt upload → AI extraction → dashboard with reminders (must loop continuously)

## 🔑 Critical Requirements

### Design Philosophy - UNLIMITED CREATIVITY
- **Style**: Futuristic, AI-inspired, 2024-2025 design trends
- **No Templates**: Custom layouts with multi-layer, scroll motion, animated headlines
- **EVERY Section/Page Effects**: Each section on every page MUST have unique effects/animations/interactions
- **Unlimited Effect Pool**: Not limited to listed effects - explore ALL possible CSS, GSAP, Three.js, WebGL capabilities
- **Hero Section**: MANDATORY working mini demo animation that loops continuously showing core functionality
- **Multi-Layer Backgrounds**: 3-10+ image layers with parallax on HomePage
- **Responsive**: Perfect scaling on desktop and mobile
- **Accessibility**: Proper contrast, font sizes, spacing, readability

### Demo & Simulation Requirements
- **Real Functionality**: No static/dummy designs - simulate backend with JSON/localStorage/cookies
- **File Handling**: Realistic upload behaviors for files/images/audio
- **3+ Demo Levels**: Showcase multiple layers of functionality
- **Production Quality**: Design as if launching today

## 🎨 UNLIMITED Effects Strategy

### EXPANDED Effect Pool - Use ANY/ALL Possible Effects
**Scroll & Parallax**: Multi-layer parallax backgrounds, horizontal scroll, scroll-triggered morphing, velocity-based animations, infinite carousels, scroll-hijacking, sticky transforms

**3D & WebGL**: Three.js particles, 3D model interactions, WebGL shaders, 3D transitions, camera movements, 3D typography, volumetric lighting, post-processing

**Advanced CSS**: Grid/Flexbox morphing, clip-path animations, SVG paths, CSS filters/blend modes, transform-origin animations, 3D CSS, custom easing

**Interactive & Hover**: Magnetic cursor, element following mouse, hover distortions, interactive blobs, tilt/gyroscope, voice/audio reactive, gesture-based

**Text & Typography**: Character-by-character animations, text scramble/decode, morphing shapes, kinetic typography, text along paths, variable fonts, particle explosions

**Background & Atmospheric**: Animated gradients, noise/grain, floating particles, geometric patterns, liquid/fluid simulations, matrix rain, aurora/waves, breathing/pulsing

**UI Components**: Morphing buttons, liquid navigation, animated icons, progressive reveals, staggered lists, card flips, physics accordions

**Advanced Interactions**: Drag/drop with physics, swipe gestures, multi-touch, scroll storytelling, interactive timelines, data visualizations, real-time charts

### Multi-Layer Background System for HomePage
**Layer Structure (Bottom to Top):**
1. **Base Layer**: Animated gradient or solid color
2. **Particle Layer**: Floating geometric shapes or particles
3. **Pattern Layer**: Subtle geometric patterns with opacity
4. **Image Layer 1**: Background imagery with slow parallax
5. **Image Layer 2**: Mid-ground elements with medium parallax
6. **Image Layer 3**: Foreground elements with fast parallax
7. **Interactive Layer**: Mouse-responsive elements
8. **Overlay Layer**: Subtle texture or noise
9. **Content Layer**: Actual text and UI elements
10. **Effect Layer**: Additional visual effects (glows, highlights)

## 📄 Page Specifications

### HomePage (Priority #1)
**Hero Section CRITICAL Requirements:**
- **NO TEMPLATES**: Completely unique design based on independent research
- **MANDATORY Mini Demo**: Working animation loop showing WarrantyAI's core functionality
- **Production Demo**: Receipt upload → AI extraction → dashboard display (real simulation)
- **Instant Value**: User understands product within 3 seconds
- **Zero Issues**: No bugs, flashing, loading delays, broken animations
- **Perfect Responsive**: Flawless on all devices
- **Unique Layout**: Never-before-seen hero design

**Required Sections (Each with Unique Effects):**
- **Problem/Solution** (Effect: Morphing problem → solution visualization)
- **3-Step Process** (Effect: Interactive step-by-step animation)
- **MVP Feature Preview** (Effect: 3-10 layer parallax showcase)
- **Competitor Comparison** (Effect: Animated comparison table)
- **Social Proof** (Effect: Floating testimonial cards)
- **Value Proposition** (Effect: Kinetic typography with counters)
- **Feature Carousel** (Effect: 3D carousel with physics)
- **Pricing Plans** (Effect: Hover morphing cards, equal heights)
- **Trust Elements** (Effect: Pulsing trust badges)
- **Early Adopter** (Effect: Countdown timer with particles)

### DemoPage (Priority #2)
- **Functional MVP Simulation**: Working demos, not just visuals
- **Multi-Level Experience**: 3-10+ demo layers/scenarios
- **Backend Simulation**: JSON/localStorage/cookies for data persistence
- **Production Quality**: Real content, proper layouts, smooth interactions
- **File Upload Simulation**: Realistic file handling behaviors

## 🛠️ Technical Setup

### Project Initialization
```bash
npx create-next-app@latest . --use-npm --typescript --eslint --tailwind --app --src-dir --import-alias="@/*"
```

### Dependencies
```bash
# Animation and 3D libraries
npm install gsap three @types/three framer-motion

# UI and utility libraries
npm install lucide-react clsx tailwind-merge

# Advanced animation libraries
npm install lenis @studio-freight/lenis

# Development dependencies
npm install -D @types/node @types/react @types/react-dom
```

### Enhanced Tailwind Configuration
Replace `tailwind.config.js`:
```javascript
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        background: "var(--background)",
        foreground: "var(--foreground)",
        primary: {
          50: '#eff6ff', 500: '#3b82f6', 600: '#2563eb', 700: '#1d4ed8', 900: '#1e3a8a',
        },
        accent: {
          400: '#c084fc', 500: '#a855f7', 600: '#9333ea',
        },
        neon: {
          blue: '#00f5ff', cyan: '#00ffff', purple: '#bf00ff',
          green: '#39ff14', pink: '#ff1493', orange: '#ff4500',
        }
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
        mono: ['JetBrains Mono', 'Fira Code', 'monospace'],
        display: ['Orbitron', 'Exo 2', 'sans-serif'],
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.6s ease-out',
        'glow': 'glow 2s ease-in-out infinite alternate',
        'pulse-glow': 'pulseGlow 1.5s ease-in-out infinite',
        'float': 'float 3s ease-in-out infinite',
        'wiggle': 'wiggle 1s ease-in-out infinite',
        'shake': 'shake 0.5s ease-in-out infinite',
        'flip': 'flip 0.6s ease-in-out',
        'scale-in': 'scaleIn 0.5s ease-out',
        'rotate-in': 'rotateIn 0.6s ease-out',
        'matrix': 'matrix 20s linear infinite',
        'particle': 'particle 10s ease-in-out infinite',
      },
      keyframes: {
        fadeIn: { '0%': { opacity: '0' }, '100%': { opacity: '1' } },
        slideUp: { '0%': { transform: 'translateY(20px)', opacity: '0' }, '100%': { transform: 'translateY(0)', opacity: '1' } },
        glow: { '0%': { boxShadow: '0 0 5px currentColor' }, '100%': { boxShadow: '0 0 20px currentColor, 0 0 30px currentColor' } },
        pulseGlow: { '0%, 100%': { boxShadow: '0 0 5px currentColor' }, '50%': { boxShadow: '0 0 20px currentColor, 0 0 30px currentColor, 0 0 40px currentColor' } },
        float: { '0%, 100%': { transform: 'translateY(0px)' }, '50%': { transform: 'translateY(-10px)' } },
        wiggle: { '0%, 100%': { transform: 'rotate(-3deg)' }, '50%': { transform: 'rotate(3deg)' } },
        shake: { '0%, 100%': { transform: 'translateX(0)' }, '10%, 30%, 50%, 70%, 90%': { transform: 'translateX(-2px)' }, '20%, 40%, 60%, 80%': { transform: 'translateX(2px)' } },
        flip: { '0%': { transform: 'rotateY(0)' }, '100%': { transform: 'rotateY(180deg)' } },
        scaleIn: { '0%': { transform: 'scale(0)', opacity: '0' }, '100%': { transform: 'scale(1)', opacity: '1' } },
        rotateIn: { '0%': { transform: 'rotate(-180deg) scale(0)', opacity: '0' }, '100%': { transform: 'rotate(0) scale(1)', opacity: '1' } },
        matrix: { '0%': { transform: 'translateY(-100%)' }, '100%': { transform: 'translateY(100vh)' } },
        particle: { '0%, 100%': { transform: 'translateY(0) rotate(0deg)', opacity: '1' }, '50%': { transform: 'translateY(-20px) rotate(180deg)', opacity: '0.5' } },
      },
      backdropBlur: { xs: '2px' },
      perspective: { '1000': '1000px', '2000': '2000px' },
    },
  },
  plugins: [],
}
```

### Project Structure
```bash
# Create directories
mkdir -p src/components/{ui,sections,effects,animations}
mkdir -p src/{lib,hooks}
mkdir -p public/assets/{images,icons,models}

# Create files
touch README.md research.md development.md todoList.md
```

### Utility Functions
Create `src/lib/utils.ts`:
```typescript
import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export const easeInOutCubic = (t: number): number => {
  return t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1
}

export const lerp = (start: number, end: number, factor: number): number => {
  return start + (end - start) * factor
}

export const clamp = (value: number, min: number, max: number): number => {
  return Math.min(Math.max(value, min), max)
}
```

## 🎯 Asset Resources
**Images**: Unsplash, Pexels, Pixabay  
**Vectors**: Vecteezy, unDraw, ManyPixels  
**3D Assets**: Kenney.nl, Sketchfab  
**Icons**: Lucide React, Heroicons  
**Favicon**: `https://raw.githubusercontent.com/HunterHo07/Portfolio_1/refs/heads/main/images/logo.webp`

## ✅ Quality Standards
- Zero build errors, 404s, or broken animations
- Hero animation loads perfectly and loops
- All demos function correctly with real simulations
- Responsive design on all devices
- 60fps animations, proper contrast ratios
- Real content (no Lorem ipsum)
- Production-ready quality

## 🚀 Execution
1. **Setup**: Run commands above, verify with `npm run dev`
2. **Research**: Document design trends, competitors
3. **Core Pages**: Home + Demo priority
4. **Effects**: GSAP + Three.js integration
5. **Content**: Real assets and copy
6. **QA**: Performance + accessibility testing

**Next Action**: Run setup commands, then begin with HomePage hero section featuring the working WarrantyAI demo simulation.
